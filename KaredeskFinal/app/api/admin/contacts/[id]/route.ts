import { NextRequest, NextResponse } from "next/server"
import { getContactById, addContactActivity } from "@/lib/database"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const contactId = parseInt(params.id)
    
    if (isNaN(contactId)) {
      return NextResponse.json(
        { success: false, error: "ID de contacto inválido" },
        { status: 400 }
      )
    }

    const contactData = await getContactById(contactId)
    
    if (!contactData) {
      return NextResponse.json(
        { success: false, error: "Contacto no encontrado" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: contactData
    })

  } catch (error) {
    console.error("Error fetching contact:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const contactId = parseInt(params.id)
    const body = await request.json()
    const { activity_type, description, performed_by } = body

    if (isNaN(contactId)) {
      return NextResponse.json(
        { success: false, error: "ID de contacto inválido" },
        { status: 400 }
      )
    }

    if (!activity_type || !description) {
      return NextResponse.json(
        { success: false, error: "activity_type y description son requeridos" },
        { status: 400 }
      )
    }

    await addContactActivity(contactId, activity_type, description, performed_by)

    return NextResponse.json({
      success: true,
      message: "Actividad agregada exitosamente"
    })

  } catch (error) {
    console.error("Error adding activity:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}
