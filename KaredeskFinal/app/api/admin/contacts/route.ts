import { NextRequest, NextResponse } from "next/server"
import { getContacts, getContactStats, updateContactStatus, addContactActivity } from "@/lib/database"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const filters = {
      status: searchParams.get('status') || undefined,
      service: searchParams.get('service') || undefined,
      priority: searchParams.get('priority') || undefined,
      search: searchParams.get('search') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,
    }

    const contacts = await getContacts(filters)
    
    return NextResponse.json({
      success: true,
      data: contacts,
      count: contacts.length
    })

  } catch (error) {
    console.error("Error fetching contacts:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { contactId, status, notes, assigned_to, activity_description } = body

    if (!contactId || !status) {
      return NextResponse.json(
        { success: false, error: "contactId y status son requeridos" },
        { status: 400 }
      )
    }

    // Actualizar el estado del contacto
    await updateContactStatus(contactId, status, notes, assigned_to)

    // Agregar actividad personalizada si se proporciona
    if (activity_description) {
      await addContactActivity(
        contactId,
        "manual_update",
        activity_description,
        assigned_to || "<EMAIL>"
      )
    }

    return NextResponse.json({
      success: true,
      message: "Contacto actualizado exitosamente"
    })

  } catch (error) {
    console.error("Error updating contact:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}
