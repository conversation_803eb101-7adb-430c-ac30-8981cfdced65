"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Search,
  Eye,
  Phone,
  Mail,
  BarChart3,
  RefreshCw,
  Loader2,
} from "lucide-react"
import { motion } from "framer-motion"
import Link from "next/link"
import Image from "next/image"
import {
  type Contact,
  type ContactStats,
  getContacts,
  getContactStats,
} from "@/lib/database"

export default function AdminDashboard() {
  const [contacts, setContacts] = useState<Contact[]>([])
  const [stats, setStats] = useState<ContactStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")

  const loadData = async () => {
    setLoading(true)
    try {
      const [contactsData, statsData] = await Promise.all([
        getContacts({ limit: 50 }),
        getContactStats()
      ])
      setContacts(contactsData)
      setStats(statsData)
    } catch (error) {
      console.error("Error loading data:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [])

  // Filtrar contactos
  const filteredContacts = contacts.filter((contact) => {
    const matchesSearch = searchTerm === "" || 
      contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (contact.company && contact.company.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesStatus = statusFilter === "all" || contact.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new": return "bg-blue-600/20 text-blue-400 border-blue-600/30"
      case "contacted": return "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
      case "in_progress": return "bg-purple-600/20 text-purple-400 border-purple-600/30"
      case "quoted": return "bg-orange-600/20 text-orange-400 border-orange-600/30"
      case "closed": return "bg-green-600/20 text-green-400 border-green-600/30"
      case "lost": return "bg-red-600/20 text-red-400 border-red-600/30"
      default: return "bg-slate-600/20 text-slate-400 border-slate-600/30"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent": return "bg-red-600/20 text-red-400 border-red-600/30"
      case "high": return "bg-orange-600/20 text-orange-400 border-orange-600/30"
      case "medium": return "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
      case "low": return "bg-green-600/20 text-green-400 border-green-600/30"
      default: return "bg-slate-600/20 text-slate-400 border-slate-600/30"
    }
  }

  if (loading && !stats) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-16 h-16 text-teal-600 animate-spin mx-auto mb-4" />
          <p className="text-white text-lg">Cargando dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 font-inter">
      {/* Navigation */}
      <nav className="bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3 hover:scale-105 transition-transform">
            <Image src="/logo.png" alt="Karedesk" width={40} height={40} className="w-10 h-10" />
            <span className="text-2xl font-bold text-white tracking-tight">Karedesk Admin</span>
          </Link>
          <div className="flex items-center space-x-4">
            <Button
              onClick={loadData}
              variant="ghost"
              size="sm"
              className="text-slate-300 hover:text-white rounded-2xl"
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4 mr-2" />
              )}
              Actualizar
            </Button>
            <Link href="/">
              <Button variant="outline" className="rounded-2xl">
                Ver Sitio Web
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-white mb-2 tracking-tight">Dashboard de Consultas</h1>
          <p className="text-slate-400 text-lg">Gestiona y da seguimiento a todas las consultas de clientes</p>
        </motion.div>

        {/* Estadísticas */}
        {stats && (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm font-medium">Total Consultas</p>
                    <p className="text-3xl font-bold text-white">{stats.total}</p>
                  </div>
                  <div className="w-12 h-12 bg-teal-600/20 rounded-2xl flex items-center justify-center">
                    <Users className="w-6 h-6 text-teal-400" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <span className="text-green-400">+{stats.this_month} este mes</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm font-medium">Nuevas</p>
                    <p className="text-3xl font-bold text-white">{stats.new}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-600/20 rounded-2xl flex items-center justify-center">
                    <AlertCircle className="w-6 h-6 text-blue-400" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <Clock className="w-4 h-4 text-yellow-400 mr-1" />
                  <span className="text-yellow-400">Requieren atención</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm font-medium">Tasa de Respuesta</p>
                    <p className="text-3xl font-bold text-white">{stats.response_rate}%</p>
                  </div>
                  <div className="w-12 h-12 bg-green-600/20 rounded-2xl flex items-center justify-center">
                    <CheckCircle className="w-6 h-6 text-green-400" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <BarChart3 className="w-4 h-4 text-green-400 mr-1" />
                  <span className="text-green-400">Excelente rendimiento</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm font-medium">Tiempo Promedio</p>
                    <p className="text-3xl font-bold text-white">{stats.avg_response_time}h</p>
                  </div>
                  <div className="w-12 h-12 bg-purple-600/20 rounded-2xl flex items-center justify-center">
                    <Clock className="w-6 h-6 text-purple-400" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <TrendingUp className="w-4 h-4 text-purple-400 mr-1" />
                  <span className="text-purple-400">Tiempo de respuesta</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Filtros */}
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                    <Input
                      placeholder="Buscar por nombre, email o empresa..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 bg-slate-800/50 border-slate-600 text-white rounded-2xl"
                    />
                  </div>
                </div>
                <div className="flex flex-wrap gap-4">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full sm:w-40 bg-slate-800/50 border-slate-600 text-white rounded-2xl">
                      <SelectValue placeholder="Estado" />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-800 border-slate-600">
                      <SelectItem value="all">Todos los estados</SelectItem>
                      <SelectItem value="new">Nuevo</SelectItem>
                      <SelectItem value="contacted">Contactado</SelectItem>
                      <SelectItem value="in_progress">En Progreso</SelectItem>
                      <SelectItem value="quoted">Cotizado</SelectItem>
                      <SelectItem value="closed">Cerrado</SelectItem>
                      <SelectItem value="lost">Perdido</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Tabla de consultas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
            <CardHeader className="p-6">
              <CardTitle className="text-white text-xl tracking-tight">
                Consultas ({loading ? "..." : filteredContacts.length})
              </CardTitle>
              <CardDescription className="text-slate-400">Lista de todas las consultas recibidas</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                {loading && contacts.length === 0 ? (
                  <div className="flex justify-center items-center h-64">
                    <Loader2 className="w-8 h-8 text-teal-500 animate-spin" />
                  </div>
                ) : filteredContacts.length === 0 && !loading ? (
                  <div className="text-center py-10 text-slate-400">
                    No se encontraron consultas con los filtros actuales.
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow className="border-slate-700">
                        <TableHead className="text-slate-300 font-medium">Cliente</TableHead>
                        <TableHead className="text-slate-300 font-medium">Servicio</TableHead>
                        <TableHead className="text-slate-300 font-medium">Estado</TableHead>
                        <TableHead className="text-slate-300 font-medium">Prioridad</TableHead>
                        <TableHead className="text-slate-300 font-medium">Fecha</TableHead>
                        <TableHead className="text-slate-300 font-medium">Acciones</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredContacts.map((contact) => (
                        <TableRow key={contact.id} className="border-slate-700 hover:bg-slate-800/30">
                          <TableCell>
                            <div>
                              <div className="font-medium text-white">{contact.name}</div>
                              <div className="text-sm text-slate-400">{contact.email}</div>
                              {contact.company && <div className="text-sm text-slate-500">{contact.company}</div>}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-slate-300">{contact.service}</div>
                            {contact.budget && <div className="text-sm text-slate-500">${contact.budget}</div>}
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(contact.status)}>{contact.status}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getPriorityColor(contact.priority)}>{contact.priority}</Badge>
                          </TableCell>
                          <TableCell className="text-slate-400">
                            {new Date(contact.created_at).toLocaleDateString("es-ES", {
                              year: "numeric",
                              month: "short",
                              day: "numeric",
                            })}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(`mailto:${contact.email}`)}
                                className="text-slate-400 hover:text-white"
                              >
                                <Mail className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(`tel:${contact.phone}`)}
                                className="text-slate-400 hover:text-white"
                              >
                                <Phone className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
