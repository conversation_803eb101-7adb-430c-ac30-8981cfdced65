"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import {
  ArrowLeft,
  Mail,
  Phone,
  Building,
  Calendar,
  DollarSign,
  Clock,
  User,
  MessageSquare,
  Save,
  Loader2,
  Activity,
} from "lucide-react"
import { motion } from "framer-motion"
import Link from "next/link"
import { type Contact, type ContactActivity, getContactById, updateContactStatus, addContactActivity } from "@/lib/database"

export default function ContactDetailPage() {
  const params = useParams()
  const router = useRouter()
  const contactId = parseInt(params.id as string)
  
  const [contact, setContact] = useState<Contact | null>(null)
  const [activities, setActivities] = useState<ContactActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [newNote, setNewNote] = useState("")
  const [status, setStatus] = useState("")
  const [assignedTo, setAssignedTo] = useState("")

  const loadContactData = async () => {
    setLoading(true)
    try {
      const data = await getContactById(contactId)
      if (data) {
        setContact(data.contact)
        setActivities(data.activities)
        setStatus(data.contact.status)
        setAssignedTo(data.contact.assigned_to || "")
      }
    } catch (error) {
      console.error("Error loading contact:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (contactId) {
      loadContactData()
    }
  }, [contactId])

  const handleSave = async () => {
    if (!contact) return
    
    setSaving(true)
    try {
      await updateContactStatus(contact.id, status as Contact["status"], newNote, assignedTo)
      
      if (newNote.trim()) {
        await addContactActivity(
          contact.id,
          "note_added",
          newNote,
          assignedTo || "<EMAIL>"
        )
        setNewNote("")
      }
      
      await loadContactData()
    } catch (error) {
      console.error("Error saving contact:", error)
    } finally {
      setSaving(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new": return "bg-blue-600/20 text-blue-400 border-blue-600/30"
      case "contacted": return "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
      case "in_progress": return "bg-purple-600/20 text-purple-400 border-purple-600/30"
      case "quoted": return "bg-orange-600/20 text-orange-400 border-orange-600/30"
      case "closed": return "bg-green-600/20 text-green-400 border-green-600/30"
      case "lost": return "bg-red-600/20 text-red-400 border-red-600/30"
      default: return "bg-slate-600/20 text-slate-400 border-slate-600/30"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent": return "bg-red-600/20 text-red-400 border-red-600/30"
      case "high": return "bg-orange-600/20 text-orange-400 border-orange-600/30"
      case "medium": return "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
      case "low": return "bg-green-600/20 text-green-400 border-green-600/30"
      default: return "bg-slate-600/20 text-slate-400 border-slate-600/30"
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-16 h-16 text-teal-600 animate-spin mx-auto mb-4" />
          <p className="text-white text-lg">Cargando contacto...</p>
        </div>
      </div>
    )
  }

  if (!contact) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-white text-lg mb-4">Contacto no encontrado</p>
          <Link href="/admin">
            <Button>Volver al Dashboard</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 font-inter">
      {/* Navigation */}
      <nav className="bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver
              </Button>
            </Link>
            <h1 className="text-xl font-bold text-white">Detalle de Contacto</h1>
          </div>
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-teal-600 hover:bg-teal-700"
          >
            {saving ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            Guardar Cambios
          </Button>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Contact Information */}
          <div className="lg:col-span-2 space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-white text-2xl">{contact.name}</CardTitle>
                      <CardDescription className="text-slate-400 text-lg">{contact.email}</CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      <Badge className={getStatusColor(contact.status)}>{contact.status}</Badge>
                      <Badge className={getPriorityColor(contact.priority)}>{contact.priority}</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Phone className="w-5 h-5 text-teal-400" />
                        <span className="text-slate-300">{contact.phone}</span>
                      </div>
                      {contact.company && (
                        <div className="flex items-center space-x-3">
                          <Building className="w-5 h-5 text-teal-400" />
                          <span className="text-slate-300">{contact.company}</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-3">
                        <Calendar className="w-5 h-5 text-teal-400" />
                        <span className="text-slate-300">
                          {new Date(contact.created_at).toLocaleDateString("es-ES", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </span>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <MessageSquare className="w-5 h-5 text-teal-400" />
                        <span className="text-slate-300">{contact.service}</span>
                      </div>
                      {contact.budget && (
                        <div className="flex items-center space-x-3">
                          <DollarSign className="w-5 h-5 text-teal-400" />
                          <span className="text-slate-300">{contact.budget}</span>
                        </div>
                      )}
                      {contact.timeline && (
                        <div className="flex items-center space-x-3">
                          <Clock className="w-5 h-5 text-teal-400" />
                          <span className="text-slate-300">{contact.timeline}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <Separator className="bg-slate-700" />
                  
                  <div>
                    <h3 className="text-white font-semibold mb-3">Mensaje</h3>
                    <p className="text-slate-300 leading-relaxed">{contact.message}</p>
                  </div>
                  
                  {contact.notes && (
                    <>
                      <Separator className="bg-slate-700" />
                      <div>
                        <h3 className="text-white font-semibold mb-3">Notas</h3>
                        <p className="text-slate-300 leading-relaxed">{contact.notes}</p>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Actions and Activities */}
          <div className="space-y-6">
            {/* Actions */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
                <CardHeader>
                  <CardTitle className="text-white">Acciones</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="status" className="text-slate-300">Estado</Label>
                    <Select value={status} onValueChange={setStatus}>
                      <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-800 border-slate-600">
                        <SelectItem value="new">Nuevo</SelectItem>
                        <SelectItem value="contacted">Contactado</SelectItem>
                        <SelectItem value="in_progress">En Progreso</SelectItem>
                        <SelectItem value="quoted">Cotizado</SelectItem>
                        <SelectItem value="closed">Cerrado</SelectItem>
                        <SelectItem value="lost">Perdido</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="assigned_to" className="text-slate-300">Asignado a</Label>
                    <Input
                      id="assigned_to"
                      value={assignedTo}
                      onChange={(e) => setAssignedTo(e.target.value)}
                      placeholder="<EMAIL>"
                      className="bg-slate-800/50 border-slate-600 text-white"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="note" className="text-slate-300">Agregar Nota</Label>
                    <Textarea
                      id="note"
                      value={newNote}
                      onChange={(e) => setNewNote(e.target.value)}
                      placeholder="Escribe una nota sobre este contacto..."
                      className="bg-slate-800/50 border-slate-600 text-white"
                      rows={3}
                    />
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => window.open(`mailto:${contact.email}`)}
                      variant="outline"
                      size="sm"
                      className="flex-1"
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Email
                    </Button>
                    <Button
                      onClick={() => window.open(`tel:${contact.phone}`)}
                      variant="outline"
                      size="sm"
                      className="flex-1"
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Llamar
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Activities */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Activity className="w-5 h-5 mr-2" />
                    Actividad
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {activities.map((activity) => (
                      <div key={activity.id} className="border-l-2 border-teal-600 pl-4 pb-4">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-white">
                            {activity.activity_type.replace(/_/g, " ").toUpperCase()}
                          </span>
                          <span className="text-xs text-slate-400">
                            {new Date(activity.created_at).toLocaleDateString("es-ES", {
                              month: "short",
                              day: "numeric",
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </span>
                        </div>
                        <p className="text-sm text-slate-300">{activity.description}</p>
                        {activity.performed_by && (
                          <p className="text-xs text-slate-500 mt-1">
                            Por: {activity.performed_by}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}
