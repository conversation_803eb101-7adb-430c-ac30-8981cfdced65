"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  CheckCircle,
  Brain,
  Zap,
  BarChart3,
  Cpu,
  Database,
  TrendingUp,
  Lightbulb,
  Cog,
  Target,
  Star,
  Bot,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import ContactForm from "@/components/contact-form"

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

export default function ConsultoriaIA() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 font-inter">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center hover:scale-105 transition-transform">
            <Image src="/logo.png" alt="Karedesk" width={40} height={40} className="w-10 h-10" />
          </Link>
          <Link href="/">
            <Button variant="ghost" className="text-slate-300 hover:text-white rounded-2xl">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver al inicio
            </Button>
          </Link>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.div
              className="w-24 h-24 bg-purple-600/20 rounded-3xl flex items-center justify-center mx-auto mb-8"
              whileHover={{ rotate: 360, scale: 1.1 }}
              transition={{ duration: 0.6 }}
            >
              <Brain className="w-12 h-12 text-purple-400" />
            </motion.div>
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 tracking-tight">
              Consultoría en
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                Inteligencia Artificial
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto font-light leading-relaxed">
              Transforma tu negocio con soluciones de IA personalizadas. Automatización inteligente, análisis predictivo
              y optimización de procesos
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Overview */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              Soluciones de IA que Ofrecemos
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto font-light">
              Implementamos tecnologías de inteligencia artificial adaptadas a las necesidades específicas de tu empresa
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Bot,
                title: "Chatbots Inteligentes",
                description: "Asistentes virtuales que mejoran la atención al cliente 24/7",
                features: ["Procesamiento de lenguaje natural", "Integración multicanal", "Aprendizaje continuo"],
                color: "purple",
              },
              {
                icon: BarChart3,
                title: "Análisis Predictivo",
                description: "Modelos de machine learning para predecir tendencias y comportamientos",
                features: ["Forecasting avanzado", "Detección de patrones", "Insights accionables"],
                color: "blue",
              },
              {
                icon: Zap,
                title: "Automatización de Procesos",
                description: "RPA inteligente para optimizar operaciones repetitivas",
                features: ["Automatización robótica", "Flujos de trabajo inteligentes", "Reducción de errores"],
                color: "green",
              },
              {
                icon: Database,
                title: "Análisis de Datos",
                description: "Extracción de insights valiosos de grandes volúmenes de datos",
                features: ["Big Data processing", "Visualización avanzada", "Reportes automáticos"],
                color: "yellow",
              },
              {
                icon: Target,
                title: "Sistemas de Recomendación",
                description: "Algoritmos personalizados para mejorar la experiencia del usuario",
                features: ["Personalización avanzada", "Filtrado colaborativo", "Optimización de conversiones"],
                color: "pink",
              },
              {
                icon: Cpu,
                title: "Computer Vision",
                description: "Procesamiento y análisis inteligente de imágenes y videos",
                features: ["Reconocimiento de objetos", "Análisis de calidad", "Automatización visual"],
                color: "indigo",
              },
            ].map((service, index) => (
              <motion.div
                key={service.title}
                variants={fadeInUp}
                whileHover={{ y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 hover:border-purple-500 transition-all duration-300 h-full rounded-3xl">
                  <CardHeader className="p-8">
                    <motion.div
                      className={`w-16 h-16 bg-${service.color}-600/20 rounded-2xl flex items-center justify-center mb-6`}
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <service.icon className={`w-8 h-8 text-${service.color}-400`} />
                    </motion.div>
                    <CardTitle className="text-white text-xl font-bold mb-3 tracking-tight">{service.title}</CardTitle>
                    <CardDescription className="text-slate-400 font-light leading-relaxed mb-6">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="px-8 pb-8">
                    <ul className="space-y-3">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-slate-300 font-medium">
                          <CheckCircle className={`w-4 h-4 text-${service.color}-400 mr-3`} />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Implementation Process */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">Proceso de Implementación</h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto font-light">
              Metodología estructurada para garantizar el éxito de tu proyecto de IA
            </p>
          </motion.div>

          <div className="grid md:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Análisis y Estrategia",
                description: "Evaluamos tus necesidades y definimos la estrategia de IA más adecuada",
                icon: Lightbulb,
              },
              {
                step: "02",
                title: "Diseño de Solución",
                description: "Creamos la arquitectura y seleccionamos las tecnologías apropiadas",
                icon: Cog,
              },
              {
                step: "03",
                title: "Desarrollo e Integración",
                description: "Implementamos la solución y la integramos con tus sistemas existentes",
                icon: Cpu,
              },
              {
                step: "04",
                title: "Optimización Continua",
                description: "Monitoreamos el rendimiento y optimizamos los modelos constantemente",
                icon: TrendingUp,
              },
            ].map((process, index) => (
              <motion.div key={process.step} variants={fadeInUp} className="text-center">
                <Card className="bg-slate-900/50 border-slate-700 hover:border-purple-500 transition-all duration-300 rounded-3xl">
                  <CardContent className="p-8">
                    <div className="text-6xl font-bold text-purple-400/20 mb-4">{process.step}</div>
                    <motion.div
                      className="w-16 h-16 bg-purple-600/20 rounded-2xl flex items-center justify-center mx-auto mb-6"
                      whileHover={{ scale: 1.1, rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <process.icon className="w-8 h-8 text-purple-400" />
                    </motion.div>
                    <h3 className="text-white font-bold text-lg mb-3 tracking-tight">{process.title}</h3>
                    <p className="text-slate-400 font-light leading-relaxed">{process.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Use Cases */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div variants={fadeInUp}>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">Casos de Uso Exitosos</h2>
              <p className="text-xl text-slate-300 mb-8 font-light leading-relaxed">
                Descubre cómo la IA puede transformar diferentes aspectos de tu negocio
              </p>
              <div className="space-y-6">
                {[
                  {
                    title: "E-commerce Inteligente",
                    description: "Sistemas de recomendación que aumentan las ventas hasta un 35%",
                    impact: "+35% ventas",
                  },
                  {
                    title: "Atención al Cliente Automatizada",
                    description: "Chatbots que resuelven el 80% de consultas sin intervención humana",
                    impact: "80% automatización",
                  },
                  {
                    title: "Análisis Predictivo de Inventario",
                    description: "Optimización de stock que reduce costos de almacenamiento en 25%",
                    impact: "-25% costos",
                  },
                  {
                    title: "Detección de Fraudes",
                    description: "Algoritmos que identifican transacciones sospechosas en tiempo real",
                    impact: "99.5% precisión",
                  },
                ].map((useCase, index) => (
                  <motion.div
                    key={useCase.title}
                    className="flex items-start space-x-4"
                    initial={{ opacity: 0, x: -50 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.2 }}
                    whileHover={{ x: 10 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0 mt-1"
                      whileHover={{ scale: 1.2, rotate: 360 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Brain className="w-5 h-5 text-white" />
                    </motion.div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-white font-bold text-lg tracking-tight">{useCase.title}</h3>
                        <Badge className="bg-purple-600/20 text-purple-400 border-purple-600/30">
                          {useCase.impact}
                        </Badge>
                      </div>
                      <p className="text-slate-400 font-light leading-relaxed">{useCase.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div variants={fadeInUp}>
              <Card className="bg-gradient-to-br from-purple-600/20 to-pink-600/20 border-slate-700 rounded-3xl">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold text-white mb-6 tracking-tight">Impacto Medible</h3>
                  <div className="grid grid-cols-2 gap-6">
                    {[
                      { number: "40%", label: "Reducción de Costos" },
                      { number: "60%", label: "Aumento de Eficiencia" },
                      { number: "24/7", label: "Disponibilidad" },
                      { number: "99%", label: "Precisión de Modelos" },
                    ].map((stat, index) => (
                      <motion.div
                        key={stat.label}
                        className="text-center"
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="text-3xl font-bold text-purple-400 mb-2 tracking-tight">{stat.number}</div>
                        <div className="text-slate-300 font-medium">{stat.label}</div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Technologies */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              Tecnologías que Utilizamos
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto font-light">
              Stack tecnológico de vanguardia para desarrollar soluciones de IA robustas y escalables
            </p>
          </motion.div>

          <div className="grid md:grid-cols-4 gap-6">
            {[
              { name: "TensorFlow", category: "Machine Learning" },
              { name: "PyTorch", category: "Deep Learning" },
              { name: "OpenAI GPT", category: "Language Models" },
              { name: "Scikit-learn", category: "Data Science" },
              { name: "Hugging Face", category: "NLP" },
              { name: "Apache Spark", category: "Big Data" },
              { name: "Docker", category: "Deployment" },
              { name: "Kubernetes", category: "Orchestration" },
            ].map((tech, index) => (
              <motion.div
                key={tech.name}
                variants={fadeInUp}
                whileHover={{ y: -5, scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 hover:border-purple-500 transition-all duration-300 rounded-2xl">
                  <CardContent className="p-6 text-center">
                    <h3 className="text-white font-bold text-lg mb-2 tracking-tight">{tech.name}</h3>
                    <Badge className="bg-purple-600/20 text-purple-400 border-purple-600/30">{tech.category}</Badge>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Testimonials */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              Historias de Transformación
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                name: "Patricia Vega",
                role: "CEO, RetailTech",
                content:
                  "La implementación de IA en nuestro e-commerce aumentó las ventas en un 40%. El sistema de recomendaciones es increíblemente preciso.",
                rating: 5,
              },
              {
                name: "Miguel Torres",
                role: "Director de Operaciones, LogiCorp",
                content:
                  "El análisis predictivo nos permite optimizar nuestras rutas de entrega, reduciendo costos operativos significativamente.",
                rating: 5,
              },
            ].map((testimonial, index) => (
              <motion.div
                key={testimonial.name}
                variants={fadeInUp}
                whileHover={{ y: -5 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 h-full rounded-3xl">
                  <CardContent className="p-8">
                    <div className="flex items-center mb-6">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <p className="text-slate-300 mb-6 font-light leading-relaxed text-lg">"{testimonial.content}"</p>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-purple-600 rounded-2xl flex items-center justify-center mr-4">
                        <Brain className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <div className="text-white font-bold tracking-tight">{testimonial.name}</div>
                        <div className="text-slate-400 font-medium">{testimonial.role}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Contact Form Section */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={fadeInUp}
      >
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              ¿Listo para transformar tu negocio con IA?
            </h2>
            <p className="text-xl text-slate-300 font-light leading-relaxed max-w-3xl mx-auto">
              Descubre el potencial de la inteligencia artificial para tu empresa. Consulta gratuita con nuestros
              expertos
            </p>
          </div>

          <ContactForm
            service="Consultoría en Inteligencia Artificial"
            title="Consulta con nuestros expertos en IA"
            description="Cuéntanos sobre tus procesos actuales y te mostraremos cómo la IA puede optimizarlos"
            showBudget={true}
            showTimeline={true}
          />
        </div>
      </motion.section>

      {/* Footer */}
      <footer className="py-12 px-4 border-t border-slate-700">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <Link href="/" className="flex items-center space-x-3 mb-4 md:mb-0 hover:scale-105 transition-transform">
              <Image src="/logo.png" alt="Karedesk" width={32} height={32} className="w-8 h-8" />
              <span className="text-xl font-bold text-white tracking-tight">Karedesk</span>
            </Link>
            <div className="text-slate-400 text-center md:text-right">
              <p className="font-medium">&copy; 2024 Karedesk. Todos los derechos reservados.</p>
              <p className="text-sm mt-1 font-light">Consultoría en Inteligencia Artificial</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
