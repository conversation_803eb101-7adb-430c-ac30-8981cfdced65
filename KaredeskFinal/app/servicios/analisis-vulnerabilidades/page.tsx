"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  CheckCircle,
  Shield,
  Search,
  FileText,
  AlertTriangle,
  Lock,
  Eye,
  Target,
  Zap,
  Database,
  Globe,
  Star,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import ContactForm from "@/components/contact-form"

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

export default function AnalisisVulnerabilidades() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 font-inter">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center hover:scale-105 transition-transform">
            <Image src="/logo.png" alt="Karedesk" width={40} height={40} className="w-10 h-10" />
          </Link>
          <Link href="/">
            <Button variant="ghost" className="text-slate-300 hover:text-white rounded-2xl">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver al inicio
            </Button>
          </Link>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.div
              className="w-24 h-24 bg-red-600/20 rounded-3xl flex items-center justify-center mx-auto mb-8"
              whileHover={{ rotate: 360, scale: 1.1 }}
              transition={{ duration: 0.6 }}
            >
              <Shield className="w-12 h-12 text-red-400" />
            </motion.div>
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 tracking-tight">
              Análisis de
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-red-400 to-orange-400">
                Vulnerabilidades
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto font-light leading-relaxed">
              Protege tu negocio con auditorías de seguridad exhaustivas y pentesting profesional para identificar y
              corregir vulnerabilidades
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Overview */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              Nuestros Servicios de Seguridad
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto font-light">
              Evaluación completa de la seguridad de tu infraestructura digital
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Target,
                title: "Penetration Testing",
                description: "Simulamos ataques reales para identificar vulnerabilidades críticas",
                features: ["Ataques simulados", "Metodología OWASP", "Pruebas exhaustivas"],
                color: "red",
              },
              {
                icon: Search,
                title: "Vulnerability Assessment",
                description: "Escaneo automatizado y manual para detectar debilidades de seguridad",
                features: ["Escaneo automatizado", "Análisis manual", "Base de datos CVE"],
                color: "orange",
              },
              {
                icon: Globe,
                title: "Web Application Security",
                description: "Análisis específico de aplicaciones web y APIs",
                features: ["Inyección SQL", "XSS Testing", "Autenticación"],
                color: "yellow",
              },
              {
                icon: Database,
                title: "Network Security Audit",
                description: "Evaluación de la seguridad de tu infraestructura de red",
                features: ["Firewall testing", "Port scanning", "Network mapping"],
                color: "green",
              },
              {
                icon: Lock,
                title: "Compliance Assessment",
                description: "Verificación del cumplimiento de estándares de seguridad",
                features: ["ISO 27001", "PCI DSS", "GDPR"],
                color: "blue",
              },
              {
                icon: FileText,
                title: "Security Reporting",
                description: "Reportes detallados con recomendaciones de remediación",
                features: ["Reportes ejecutivos", "Detalles técnicos", "Plan de acción"],
                color: "purple",
              },
            ].map((service, index) => (
              <motion.div
                key={service.title}
                variants={fadeInUp}
                whileHover={{ y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 hover:border-red-500 transition-all duration-300 h-full rounded-3xl">
                  <CardHeader className="p-8">
                    <motion.div
                      className={`w-16 h-16 bg-${service.color}-600/20 rounded-2xl flex items-center justify-center mb-6`}
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <service.icon className={`w-8 h-8 text-${service.color}-400`} />
                    </motion.div>
                    <CardTitle className="text-white text-xl font-bold mb-3 tracking-tight">{service.title}</CardTitle>
                    <CardDescription className="text-slate-400 font-light leading-relaxed mb-6">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="px-8 pb-8">
                    <ul className="space-y-3">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-slate-300 font-medium">
                          <CheckCircle className={`w-4 h-4 text-${service.color}-400 mr-3`} />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Process Section */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">Metodología de Análisis</h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto font-light">
              Proceso estructurado basado en estándares internacionales de ciberseguridad
            </p>
          </motion.div>

          <div className="grid md:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Reconocimiento",
                description: "Recopilación de información sobre el objetivo y mapeo de la superficie de ataque",
                icon: Eye,
              },
              {
                step: "02",
                title: "Escaneo",
                description: "Identificación de servicios, puertos abiertos y tecnologías utilizadas",
                icon: Search,
              },
              {
                step: "03",
                title: "Explotación",
                description: "Pruebas controladas para confirmar vulnerabilidades encontradas",
                icon: Zap,
              },
              {
                step: "04",
                title: "Reporte",
                description: "Documentación detallada con recomendaciones de seguridad",
                icon: FileText,
              },
            ].map((process, index) => (
              <motion.div key={process.step} variants={fadeInUp} className="text-center">
                <Card className="bg-slate-900/50 border-slate-700 hover:border-red-500 transition-all duration-300 rounded-3xl">
                  <CardContent className="p-8">
                    <div className="text-6xl font-bold text-red-400/20 mb-4">{process.step}</div>
                    <motion.div
                      className="w-16 h-16 bg-red-600/20 rounded-2xl flex items-center justify-center mx-auto mb-6"
                      whileHover={{ scale: 1.1, rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <process.icon className="w-8 h-8 text-red-400" />
                    </motion.div>
                    <h3 className="text-white font-bold text-lg mb-3 tracking-tight">{process.title}</h3>
                    <p className="text-slate-400 font-light leading-relaxed">{process.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Threat Landscape */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div variants={fadeInUp}>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">Amenazas que Detectamos</h2>
              <p className="text-xl text-slate-300 mb-8 font-light leading-relaxed">
                Identificamos y evaluamos las vulnerabilidades más críticas que pueden comprometer tu seguridad
              </p>
              <div className="space-y-6">
                {[
                  {
                    title: "Inyección SQL",
                    description: "Detectamos vulnerabilidades que permiten acceso no autorizado a bases de datos",
                    severity: "Crítica",
                  },
                  {
                    title: "Cross-Site Scripting (XSS)",
                    description: "Identificamos puntos donde código malicioso puede ejecutarse en navegadores",
                    severity: "Alta",
                  },
                  {
                    title: "Configuraciones Inseguras",
                    description: "Evaluamos configuraciones de servidores y aplicaciones",
                    severity: "Media",
                  },
                  {
                    title: "Autenticación Débil",
                    description: "Analizamos sistemas de autenticación y control de acceso",
                    severity: "Alta",
                  },
                ].map((threat, index) => (
                  <motion.div
                    key={threat.title}
                    className="flex items-start space-x-4"
                    initial={{ opacity: 0, x: -50 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.2 }}
                    whileHover={{ x: 10 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-red-600 rounded-2xl flex items-center justify-center flex-shrink-0 mt-1"
                      whileHover={{ scale: 1.2, rotate: 360 }}
                      transition={{ duration: 0.3 }}
                    >
                      <AlertTriangle className="w-5 h-5 text-white" />
                    </motion.div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-white font-bold text-lg tracking-tight">{threat.title}</h3>
                        <Badge
                          className={`${
                            threat.severity === "Crítica"
                              ? "bg-red-600/20 text-red-400 border-red-600/30"
                              : threat.severity === "Alta"
                                ? "bg-orange-600/20 text-orange-400 border-orange-600/30"
                                : "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
                          }`}
                        >
                          {threat.severity}
                        </Badge>
                      </div>
                      <p className="text-slate-400 font-light leading-relaxed">{threat.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div variants={fadeInUp}>
              <Card className="bg-gradient-to-br from-red-600/20 to-orange-600/20 border-slate-700 rounded-3xl">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold text-white mb-6 tracking-tight">Estadísticas de Seguridad</h3>
                  <div className="grid grid-cols-2 gap-6">
                    {[
                      { number: "95%", label: "Vulnerabilidades Detectadas" },
                      { number: "24h", label: "Tiempo de Análisis" },
                      { number: "100%", label: "Cobertura OWASP Top 10" },
                      { number: "0", label: "Falsos Positivos" },
                    ].map((stat, index) => (
                      <motion.div
                        key={stat.label}
                        className="text-center"
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="text-3xl font-bold text-red-400 mb-2 tracking-tight">{stat.number}</div>
                        <div className="text-slate-300 font-medium">{stat.label}</div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Testimonials */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">Casos de Éxito</h2>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                name: "Laura Martínez",
                role: "CISO, SecureBank",
                content:
                  "El análisis de vulnerabilidades de Karedesk nos ayudó a identificar 15 vulnerabilidades críticas que no habíamos detectado. Su metodología es muy profesional.",
                rating: 5,
              },
              {
                name: "Roberto Silva",
                role: "CTO, FinTech Pro",
                content:
                  "Excelente trabajo en el pentesting de nuestra aplicación. Los reportes son muy detallados y las recomendaciones muy claras para implementar.",
                rating: 5,
              },
            ].map((testimonial, index) => (
              <motion.div
                key={testimonial.name}
                variants={fadeInUp}
                whileHover={{ y: -5 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 h-full rounded-3xl">
                  <CardContent className="p-8">
                    <div className="flex items-center mb-6">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <p className="text-slate-300 mb-6 font-light leading-relaxed text-lg">"{testimonial.content}"</p>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-red-600 rounded-2xl flex items-center justify-center mr-4">
                        <Shield className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <div className="text-white font-bold tracking-tight">{testimonial.name}</div>
                        <div className="text-slate-400 font-medium">{testimonial.role}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Contact Form Section */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={fadeInUp}
      >
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              ¿Qué tan seguro está tu negocio?
            </h2>
            <p className="text-xl text-slate-300 font-light leading-relaxed max-w-3xl mx-auto">
              Obtén una evaluación gratuita de seguridad y descubre las vulnerabilidades que podrían estar
              comprometiendo tu empresa
            </p>
          </div>

          <ContactForm
            service="Análisis de Vulnerabilidades"
            title="Solicita tu evaluación de seguridad"
            description="Cuéntanos sobre tu infraestructura y te proporcionaremos un análisis inicial gratuito"
            showBudget={true}
            showTimeline={true}
          />
        </div>
      </motion.section>

      {/* Footer */}
      <footer className="py-12 px-4 border-t border-slate-700">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <Link href="/" className="flex items-center space-x-3 mb-4 md:mb-0 hover:scale-105 transition-transform">
              <Image src="/logo.png" alt="Karedesk" width={32} height={32} className="w-8 h-8" />
              <span className="text-xl font-bold text-white tracking-tight">Karedesk</span>
            </Link>
            <div className="text-slate-400 text-center md:text-right">
              <p className="font-medium">&copy; 2024 Karedesk. Todos los derechos reservados.</p>
              <p className="text-sm mt-1 font-light">Seguridad y análisis de vulnerabilidades</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
