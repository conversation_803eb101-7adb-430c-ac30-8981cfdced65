"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  ArrowLeft,
  CheckCircle,
  Wrench,
  Clock,
  Shield,
  Monitor,
  Server,
  Smartphone,
  Headphones,
  Settings,
  AlertTriangle,
  TrendingUp,
  Users,
  Star,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import ContactForm from "@/components/contact-form"

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

export default function AsistenciaInformatica() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 font-inter">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center hover:scale-105 transition-transform">
            <Image src="/logo.png" alt="Karedesk" width={40} height={40} className="w-10 h-10" />
          </Link>
          <Link href="/">
            <Button variant="ghost" className="text-slate-300 hover:text-white rounded-2xl">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver al inicio
            </Button>
          </Link>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.div
              className="w-24 h-24 bg-teal-600/20 rounded-3xl flex items-center justify-center mx-auto mb-8"
              whileHover={{ rotate: 360, scale: 1.1 }}
              transition={{ duration: 0.6 }}
            >
              <Wrench className="w-12 h-12 text-teal-400" />
            </motion.div>
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 tracking-tight">
              Asistencia
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-teal-400 to-emerald-400">
                Informática
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto font-light leading-relaxed">
              Soporte técnico especializado 24/7 para mantener tu infraestructura tecnológica funcionando sin
              interrupciones
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Overview */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              ¿Qué incluye nuestro servicio?
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto font-light">
              Cobertura completa para todas tus necesidades tecnológicas
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Clock,
                title: "Soporte 24/7",
                description: "Disponibilidad completa todos los días del año para resolver cualquier incidencia",
                features: ["Respuesta inmediata", "Técnicos especializados", "Múltiples canales de contacto"],
              },
              {
                icon: Monitor,
                title: "Mantenimiento Preventivo",
                description: "Revisiones programadas para evitar problemas antes de que ocurran",
                features: ["Actualizaciones automáticas", "Limpieza de sistemas", "Optimización de rendimiento"],
              },
              {
                icon: Server,
                title: "Gestión de Servidores",
                description: "Administración completa de tu infraestructura de servidores",
                features: ["Monitoreo continuo", "Backups automáticos", "Configuración optimizada"],
              },
              {
                icon: Smartphone,
                title: "Soporte Remoto",
                description: "Resolución de problemas sin necesidad de visitas presenciales",
                features: ["Acceso remoto seguro", "Diagnóstico rápido", "Solución inmediata"],
              },
              {
                icon: Shield,
                title: "Seguridad IT",
                description: "Protección integral contra amenazas y vulnerabilidades",
                features: ["Antivirus empresarial", "Firewall configurado", "Políticas de seguridad"],
              },
              {
                icon: Settings,
                title: "Configuración de Sistemas",
                description: "Instalación y configuración de software y hardware",
                features: ["Software empresarial", "Hardware especializado", "Integración de sistemas"],
              },
            ].map((service, index) => (
              <motion.div
                key={service.title}
                variants={fadeInUp}
                whileHover={{ y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 hover:border-teal-500 transition-all duration-300 h-full rounded-3xl">
                  <CardHeader className="p-8">
                    <motion.div
                      className="w-16 h-16 bg-teal-600/20 rounded-2xl flex items-center justify-center mb-6"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <service.icon className="w-8 h-8 text-teal-400" />
                    </motion.div>
                    <CardTitle className="text-white text-xl font-bold mb-3 tracking-tight">{service.title}</CardTitle>
                    <CardDescription className="text-slate-400 font-light leading-relaxed mb-6">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="px-8 pb-8">
                    <ul className="space-y-3">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-slate-300 font-medium">
                          <CheckCircle className="w-4 h-4 text-teal-400 mr-3" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Process Section */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">Nuestro Proceso</h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto font-light">
              Metodología probada para resolver tus problemas técnicos de manera eficiente
            </p>
          </motion.div>

          <div className="grid md:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Contacto Inicial",
                description: "Recibimos tu solicitud por cualquier canal disponible",
                icon: Headphones,
              },
              {
                step: "02",
                title: "Diagnóstico",
                description: "Analizamos el problema y determinamos la mejor solución",
                icon: AlertTriangle,
              },
              {
                step: "03",
                title: "Implementación",
                description: "Ejecutamos la solución de manera rápida y eficiente",
                icon: Settings,
              },
              {
                step: "04",
                title: "Seguimiento",
                description: "Verificamos que todo funcione correctamente",
                icon: TrendingUp,
              },
            ].map((process, index) => (
              <motion.div key={process.step} variants={fadeInUp} className="text-center">
                <Card className="bg-slate-900/50 border-slate-700 hover:border-teal-500 transition-all duration-300 rounded-3xl">
                  <CardContent className="p-8">
                    <div className="text-6xl font-bold text-teal-400/20 mb-4">{process.step}</div>
                    <motion.div
                      className="w-16 h-16 bg-teal-600/20 rounded-2xl flex items-center justify-center mx-auto mb-6"
                      whileHover={{ scale: 1.1, rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <process.icon className="w-8 h-8 text-teal-400" />
                    </motion.div>
                    <h3 className="text-white font-bold text-lg mb-3 tracking-tight">{process.title}</h3>
                    <p className="text-slate-400 font-light leading-relaxed">{process.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Benefits Section */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div variants={fadeInUp}>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
                Beneficios de nuestro servicio
              </h2>
              <p className="text-xl text-slate-300 mb-8 font-light leading-relaxed">
                Mantén tu negocio funcionando sin interrupciones con nuestro soporte especializado
              </p>
              <div className="space-y-6">
                {[
                  {
                    title: "Reducción de Downtime",
                    description: "Minimizamos el tiempo de inactividad con respuesta rápida y soluciones eficientes",
                  },
                  {
                    title: "Ahorro de Costos",
                    description: "Evita gastos innecesarios con mantenimiento preventivo y diagnósticos precisos",
                  },
                  {
                    title: "Tranquilidad Total",
                    description: "Concéntrate en tu negocio mientras nosotros cuidamos tu tecnología",
                  },
                  {
                    title: "Escalabilidad",
                    description: "Nuestro soporte crece contigo, adaptándose a las necesidades de tu empresa",
                  },
                ].map((benefit, index) => (
                  <motion.div
                    key={benefit.title}
                    className="flex items-start space-x-4"
                    initial={{ opacity: 0, x: -50 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.2 }}
                    whileHover={{ x: 10 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-teal-600 rounded-2xl flex items-center justify-center flex-shrink-0 mt-1"
                      whileHover={{ scale: 1.2, rotate: 360 }}
                      transition={{ duration: 0.3 }}
                    >
                      <CheckCircle className="w-5 h-5 text-white" />
                    </motion.div>
                    <div>
                      <h3 className="text-white font-bold text-lg mb-2 tracking-tight">{benefit.title}</h3>
                      <p className="text-slate-400 font-light leading-relaxed">{benefit.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div variants={fadeInUp}>
              <Card className="bg-gradient-to-br from-teal-600/20 to-emerald-600/20 border-slate-700 rounded-3xl">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold text-white mb-6 tracking-tight">Estadísticas de Rendimiento</h3>
                  <div className="grid grid-cols-2 gap-6">
                    {[
                      { number: "99.9%", label: "Uptime Garantizado" },
                      { number: "<5min", label: "Tiempo de Respuesta" },
                      { number: "24/7", label: "Disponibilidad" },
                      { number: "500+", label: "Clientes Satisfechos" },
                    ].map((stat, index) => (
                      <motion.div
                        key={stat.label}
                        className="text-center"
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="text-3xl font-bold text-teal-400 mb-2 tracking-tight">{stat.number}</div>
                        <div className="text-slate-300 font-medium">{stat.label}</div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Testimonials */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              Lo que dicen nuestros clientes
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                name: "Ana Rodríguez",
                role: "Gerente IT, TechSolutions",
                content:
                  "El soporte de Karedesk es excepcional. Siempre están disponibles cuando los necesitamos y resuelven los problemas de manera rápida y eficiente.",
                rating: 5,
              },
              {
                name: "Carlos Mendoza",
                role: "CEO, InnovaDigital",
                content:
                  "Desde que contratamos sus servicios, no hemos tenido prácticamente ningún tiempo de inactividad. Su equipo es muy profesional y conoce su trabajo.",
                rating: 5,
              },
            ].map((testimonial, index) => (
              <motion.div
                key={testimonial.name}
                variants={fadeInUp}
                whileHover={{ y: -5 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 h-full rounded-3xl">
                  <CardContent className="p-8">
                    <div className="flex items-center mb-6">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <p className="text-slate-300 mb-6 font-light leading-relaxed text-lg">"{testimonial.content}"</p>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-teal-600 rounded-2xl flex items-center justify-center mr-4">
                        <Users className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <div className="text-white font-bold tracking-tight">{testimonial.name}</div>
                        <div className="text-slate-400 font-medium">{testimonial.role}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Contact Form Section */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={fadeInUp}
      >
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              ¿Necesitas soporte técnico confiable?
            </h2>
            <p className="text-xl text-slate-300 font-light leading-relaxed max-w-3xl mx-auto">
              Completa el formulario y uno de nuestros especialistas te contactará para evaluar tus necesidades de
              soporte técnico
            </p>
          </div>

          <ContactForm
            service="Asistencia Informática"
            title="Solicita tu consulta gratuita"
            description="Cuéntanos sobre tu infraestructura actual y te ayudaremos a optimizarla"
            showTimeline={true}
          />
        </div>
      </motion.section>

      {/* Footer */}
      <footer className="py-12 px-4 border-t border-slate-700">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <Link href="/" className="flex items-center space-x-3 mb-4 md:mb-0 hover:scale-105 transition-transform">
              <Image src="/logo.png" alt="Karedesk" width={32} height={32} className="w-8 h-8" />
              <span className="text-xl font-bold text-white tracking-tight">Karedesk</span>
            </Link>
            <div className="text-slate-400 text-center md:text-right">
              <p className="font-medium">&copy; 2024 Karedesk. Todos los derechos reservados.</p>
              <p className="text-sm mt-1 font-light">Soporte técnico especializado</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
