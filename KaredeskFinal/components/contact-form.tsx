"use client"

import { useActionState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, CheckCircle, Send, Mail, Phone, Clock, Zap } from "lucide-react"
import { motion } from "framer-motion"
import { submitContactForm, type ContactFormState } from "@/lib/contact-actions"

interface ContactFormProps {
  service: string
  title?: string
  description?: string
  showBudget?: boolean
  showTimeline?: boolean
}

const initialState: ContactFormState = {}

export default function ContactForm({
  service,
  title = "¿Listo para comenzar?",
  description = "Completa el formulario y te contactaremos en menos de 24 horas",
  showBudget = false,
  showTimeline = false,
}: ContactFormProps) {
  const [state, formAction, isPending] = useActionState(submitContactForm, initialState)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
    >
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="p-8">
          <CardTitle className="text-2xl font-bold text-white tracking-tight">{title}</CardTitle>
          <CardDescription className="text-slate-400 font-light text-lg">{description}</CardDescription>

          {/* Service badge */}
          <div className="flex items-center gap-2 mt-4">
            <Badge variant="tech" className="text-sm">
              <Zap className="w-3 h-3 mr-1" />
              {service}
            </Badge>
            <Badge variant="success" className="text-sm">
              <Clock className="w-3 h-3 mr-1" />
              Respuesta en 24h
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-8 pt-0">
          <form action={formAction} className="space-y-6">
            {/* Mensaje de estado */}
            {state.message && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className={`p-6 rounded-2xl flex items-start space-x-4 ${
                  state.success
                    ? "bg-green-600/20 border border-green-600/30 text-green-400"
                    : "bg-red-600/20 border border-red-600/30 text-red-400"
                }`}
              >
                <div className="flex-shrink-0 mt-1">
                  {state.success ? <CheckCircle className="w-5 h-5" /> : <AlertCircle className="w-5 h-5" />}
                </div>
                <div className="flex-1">
                  <div className="font-medium mb-2">
                    {state.success ? "¡Consulta enviada exitosamente!" : "Error al enviar consulta"}
                  </div>
                  <div className="text-sm leading-relaxed whitespace-pre-line">{state.message}</div>

                  {state.success && state.emailSent && (
                    <div className="mt-4 pt-4 border-t border-green-600/30">
                      <div className="text-sm font-medium mb-2">📧 Revisa tu email para más detalles</div>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <a
                          href="mailto:<EMAIL>"
                          className="inline-flex items-center text-sm text-green-300 hover:text-green-200 transition-colors"
                        >
                          <Mail className="w-4 h-4 mr-2" />
                          <EMAIL>
                        </a>
                        <a
                          href="tel:+15551234567"
                          className="inline-flex items-center text-sm text-green-300 hover:text-green-200 transition-colors"
                        >
                          <Phone className="w-4 h-4 mr-2" />
                          +****************
                        </a>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            )}

            {/* Solo mostrar el formulario si no se ha enviado exitosamente */}
            {!state.success && (
              <>
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Nombre */}
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-white font-medium">
                      Nombre completo *
                    </Label>
                    <Input id="name" name="name" type="text" required placeholder="Tu nombre completo" />
                    {state.errors?.name && <p className="text-red-400 text-sm">{state.errors.name[0]}</p>}
                  </div>

                  {/* Email */}
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-white font-medium">
                      Email *
                    </Label>
                    <Input id="email" name="email" type="email" required placeholder="<EMAIL>" />
                    {state.errors?.email && <p className="text-red-400 text-sm">{state.errors.email[0]}</p>}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  {/* Teléfono */}
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-white font-medium">
                      Teléfono *
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      required
                      placeholder="****** 123 4567"
                      pattern="\d{10,}"
                      minLength={10}
                    />
                    {state.errors?.phone && <p className="text-red-400 text-sm">{state.errors.phone[0]}</p>}
                  </div>

                  {/* Empresa */}
                  <div className="space-y-2">
                    <Label htmlFor="company" className="text-white font-medium">
                      Empresa
                    </Label>
                    <Input id="company" name="company" type="text" placeholder="Nombre de tu empresa" />
                  </div>
                </div>

                {/* Servicio (hidden field con valor predeterminado) */}
                <input type="hidden" name="service" value={service} />

                {/* Presupuesto y Timeline si se requieren */}
                {(showBudget || showTimeline) && (
                  <div className="grid md:grid-cols-2 gap-6">
                    {showBudget && (
                      <div className="space-y-2">
                        <Label htmlFor="budget" className="text-white font-medium">
                          Presupuesto estimado
                        </Label>
                        <Select name="budget">
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona un rango" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1000-5000">$1,000 - $5,000</SelectItem>
                            <SelectItem value="5000-10000">$5,000 - $10,000</SelectItem>
                            <SelectItem value="10000-25000">$10,000 - $25,000</SelectItem>
                            <SelectItem value="25000+">$25,000+</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {showTimeline && (
                      <div className="space-y-2">
                        <Label htmlFor="timeline" className="text-white font-medium">
                          Timeline del proyecto
                        </Label>
                        <Select name="timeline">
                          <SelectTrigger>
                            <SelectValue placeholder="¿Cuándo necesitas comenzar?" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="inmediato">Inmediatamente</SelectItem>
                            <SelectItem value="1-2-semanas">1-2 semanas</SelectItem>
                            <SelectItem value="1-mes">En 1 mes</SelectItem>
                            <SelectItem value="2-3-meses">2-3 meses</SelectItem>
                            <SelectItem value="flexible">Flexible</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                )}

                {/* Mensaje */}
                <div className="space-y-2">
                  <Label htmlFor="message" className="text-white font-medium">
                    Cuéntanos sobre tu proyecto *
                  </Label>
                  <Textarea
                    id="message"
                    name="message"
                    required
                    rows={4}
                    minLength={10}
                    placeholder="Describe tu proyecto, objetivos y cualquier detalle importante..."
                  />
                  {state.errors?.message && <p className="text-red-400 text-sm">{state.errors.message[0]}</p>}
                </div>

                {/* Botón de envío */}
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button type="submit" disabled={isPending} variant="gradient" size="lg" className="w-full">
                    {isPending ? (
                      <div className="flex items-center space-x-2">
                        <div className="spinner" />
                        <span>Enviando consulta...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Send className="w-5 h-5" />
                        <span>Enviar Consulta</span>
                      </div>
                    )}
                  </Button>
                </motion.div>

                <p className="text-slate-400 text-sm text-center font-light">
                  * Campos obligatorios. Te contactaremos en menos de 24 horas.
                </p>
              </>
            )}
          </form>
        </CardContent>
      </Card>
    </motion.div>
  )
}
