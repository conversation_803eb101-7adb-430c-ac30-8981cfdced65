"use client"

import { Shield } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { motion } from "framer-motion"

export function AdminAccessButton() {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, delay: 2 }}
      className="fixed bottom-6 right-6 z-50"
    >
      <Link href="/admin-login">
        <Button
          variant="outline"
          size="sm"
          className="bg-slate-800/80 backdrop-blur-sm border-slate-600/50 text-slate-300 hover:text-white hover:bg-slate-700/80 shadow-lg"
        >
          <Shield className="w-4 h-4 mr-2" />
          Admin
        </Button>
      </Link>
    </motion.div>
  )
} 