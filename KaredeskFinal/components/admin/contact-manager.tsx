"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Plus,
  Edit,
  Trash2,
  Mail,
  Phone,
  Calendar,
  User,
  Building,
  MessageS<PERSON>re,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react"
import { motion } from "framer-motion"
import type { Contact, ContactActivity } from "@/lib/database"

interface ContactManagerProps {
  contact: Contact
  activities: ContactActivity[]
  onUpdate: (contactId: number, updates: Partial<Contact>) => Promise<void>
  onAddActivity: (contactId: number, activity: { activity_type: string; description: string; performed_by?: string }) => Promise<void>
}

export default function ContactManager({ contact, activities, onUpdate, onAddActivity }: ContactManagerProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [isAddingActivity, setIsAddingActivity] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [editForm, setEditForm] = useState({
    status: contact.status,
    priority: contact.priority,
    assigned_to: contact.assigned_to || "",
    notes: contact.notes || ""
  })
  const [activityForm, setActivityForm] = useState({
    activity_type: "",
    description: "",
    performed_by: "<EMAIL>"
  })

  const handleUpdate = async () => {
    setIsLoading(true)
    try {
      await onUpdate(contact.id, editForm)
      setIsEditing(false)
    } catch (error) {
      console.error("Error updating contact:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddActivity = async () => {
    if (!activityForm.activity_type || !activityForm.description) return
    
    setIsLoading(true)
    try {
      await onAddActivity(contact.id, activityForm)
      setActivityForm({
        activity_type: "",
        description: "",
        performed_by: "<EMAIL>"
      })
      setIsAddingActivity(false)
    } catch (error) {
      console.error("Error adding activity:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new": return "bg-blue-600/20 text-blue-400 border-blue-600/30"
      case "contacted": return "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
      case "in_progress": return "bg-purple-600/20 text-purple-400 border-purple-600/30"
      case "quoted": return "bg-orange-600/20 text-orange-400 border-orange-600/30"
      case "closed": return "bg-green-600/20 text-green-400 border-green-600/30"
      case "lost": return "bg-red-600/20 text-red-400 border-red-600/30"
      default: return "bg-slate-600/20 text-slate-400 border-slate-600/30"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent": return "bg-red-600/20 text-red-400 border-red-600/30"
      case "high": return "bg-orange-600/20 text-orange-400 border-orange-600/30"
      case "medium": return "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
      case "low": return "bg-green-600/20 text-green-400 border-green-600/30"
      default: return "bg-slate-600/20 text-slate-400 border-slate-600/30"
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "email_sent": return <Mail className="w-4 h-4 text-blue-400" />
      case "call_made": return <Phone className="w-4 h-4 text-green-400" />
      case "meeting_scheduled": return <Calendar className="w-4 h-4 text-purple-400" />
      case "status_changed": return <CheckCircle className="w-4 h-4 text-orange-400" />
      case "note_added": return <MessageSquare className="w-4 h-4 text-slate-400" />
      default: return <AlertCircle className="w-4 h-4 text-slate-400" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Información del Contacto */}
      <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
        <CardHeader className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white text-xl">
                {contact.name}
              </CardTitle>
              <CardDescription className="text-slate-400">
                Consulta #{contact.id} - {contact.service}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(contact.status)}>
                {contact.status}
              </Badge>
              <Badge className={getPriorityColor(contact.priority)}>
                {contact.priority}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-slate-400" />
                <div>
                  <p className="text-sm font-medium text-white">Email</p>
                  <p className="text-sm text-slate-400">{contact.email}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Phone className="w-5 h-5 text-slate-400" />
                <div>
                  <p className="text-sm font-medium text-white">Teléfono</p>
                  <p className="text-sm text-slate-400">{contact.phone}</p>
                </div>
              </div>

              {contact.company && (
                <div className="flex items-center gap-3">
                  <Building className="w-5 h-5 text-slate-400" />
                  <div>
                    <p className="text-sm font-medium text-white">Empresa</p>
                    <p className="text-sm text-slate-400">{contact.company}</p>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4">
              {contact.budget && (
                <div>
                  <p className="text-sm font-medium text-white">Presupuesto</p>
                  <p className="text-sm text-slate-400">${contact.budget}</p>
                </div>
              )}

              {contact.timeline && (
                <div>
                  <p className="text-sm font-medium text-white">Timeline</p>
                  <p className="text-sm text-slate-400">{contact.timeline}</p>
                </div>
              )}

              <div>
                <p className="text-sm font-medium text-white">Fecha de Consulta</p>
                <p className="text-sm text-slate-400">
                  {new Date(contact.created_at).toLocaleDateString("es-ES", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit"
                  })}
                </p>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <p className="text-sm font-medium text-white mb-2">Mensaje</p>
            <div className="bg-slate-800/50 p-4 rounded-xl">
              <p className="text-sm text-slate-300 whitespace-pre-wrap">{contact.message}</p>
            </div>
          </div>

          <div className="flex gap-3 mt-6">
            <Button
              onClick={() => window.open(`mailto:${contact.email}`)}
              className="bg-teal-600 hover:bg-teal-700"
            >
              <Mail className="w-4 h-4 mr-2" />
              Enviar Email
            </Button>
            <Button
              onClick={() => window.open(`tel:${contact.phone}`)}
              variant="outline"
            >
              <Phone className="w-4 h-4 mr-2" />
              Llamar
            </Button>
            <Dialog open={isEditing} onOpenChange={setIsEditing}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Edit className="w-4 h-4 mr-2" />
                  Editar
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-slate-900 border-slate-700 text-white max-w-md">
                <DialogHeader>
                  <DialogTitle>Editar Contacto</DialogTitle>
                  <DialogDescription className="text-slate-400">
                    Actualizar información del contacto
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div>
                    <Label className="text-white">Estado</Label>
                    <Select 
                      value={editForm.status} 
                      onValueChange={(value) => setEditForm(prev => ({ ...prev, status: value as Contact["status"] }))}
                    >
                      <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-800 border-slate-600">
                        <SelectItem value="new">Nuevo</SelectItem>
                        <SelectItem value="contacted">Contactado</SelectItem>
                        <SelectItem value="in_progress">En Progreso</SelectItem>
                        <SelectItem value="quoted">Cotizado</SelectItem>
                        <SelectItem value="closed">Cerrado</SelectItem>
                        <SelectItem value="lost">Perdido</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-white">Prioridad</Label>
                    <Select 
                      value={editForm.priority} 
                      onValueChange={(value) => setEditForm(prev => ({ ...prev, priority: value as Contact["priority"] }))}
                    >
                      <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-800 border-slate-600">
                        <SelectItem value="low">Baja</SelectItem>
                        <SelectItem value="medium">Media</SelectItem>
                        <SelectItem value="high">Alta</SelectItem>
                        <SelectItem value="urgent">Urgente</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-white">Asignado a</Label>
                    <Input
                      value={editForm.assigned_to}
                      onChange={(e) => setEditForm(prev => ({ ...prev, assigned_to: e.target.value }))}
                      placeholder="<EMAIL>"
                      className="bg-slate-800/50 border-slate-600 text-white"
                    />
                  </div>

                  <div>
                    <Label className="text-white">Notas</Label>
                    <Textarea
                      value={editForm.notes}
                      onChange={(e) => setEditForm(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder="Notas internas..."
                      className="bg-slate-800/50 border-slate-600 text-white min-h-[80px]"
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsEditing(false)}>
                    Cancelar
                  </Button>
                  <Button onClick={handleUpdate} disabled={isLoading}>
                    {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                    Guardar
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Historial de Actividades */}
      <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
        <CardHeader className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white">Historial de Actividades</CardTitle>
              <CardDescription className="text-slate-400">
                Registro completo de interacciones
              </CardDescription>
            </div>
            <Dialog open={isAddingActivity} onOpenChange={setIsAddingActivity}>
              <DialogTrigger asChild>
                <Button size="sm" className="bg-teal-600 hover:bg-teal-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Agregar Actividad
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-slate-900 border-slate-700 text-white max-w-md">
                <DialogHeader>
                  <DialogTitle>Nueva Actividad</DialogTitle>
                  <DialogDescription className="text-slate-400">
                    Registrar nueva actividad para este contacto
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div>
                    <Label className="text-white">Tipo de Actividad</Label>
                    <Select 
                      value={activityForm.activity_type} 
                      onValueChange={(value) => setActivityForm(prev => ({ ...prev, activity_type: value }))}
                    >
                      <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                        <SelectValue placeholder="Seleccionar tipo" />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-800 border-slate-600">
                        <SelectItem value="email_sent">Email Enviado</SelectItem>
                        <SelectItem value="call_made">Llamada Realizada</SelectItem>
                        <SelectItem value="meeting_scheduled">Reunión Programada</SelectItem>
                        <SelectItem value="quote_sent">Cotización Enviada</SelectItem>
                        <SelectItem value="note_added">Nota Agregada</SelectItem>
                        <SelectItem value="follow_up">Seguimiento</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-white">Descripción</Label>
                    <Textarea
                      value={activityForm.description}
                      onChange={(e) => setActivityForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe la actividad realizada..."
                      className="bg-slate-800/50 border-slate-600 text-white min-h-[80px]"
                    />
                  </div>

                  <div>
                    <Label className="text-white">Realizado por</Label>
                    <Input
                      value={activityForm.performed_by}
                      onChange={(e) => setActivityForm(prev => ({ ...prev, performed_by: e.target.value }))}
                      placeholder="<EMAIL>"
                      className="bg-slate-800/50 border-slate-600 text-white"
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddingActivity(false)}>
                    Cancelar
                  </Button>
                  <Button onClick={handleAddActivity} disabled={isLoading || !activityForm.activity_type || !activityForm.description}>
                    {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                    Agregar
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent className="p-6 pt-0">
          {activities.length > 0 ? (
            <div className="space-y-4">
              {activities.map((activity, index) => (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-start gap-4 p-4 bg-slate-800/30 rounded-xl"
                >
                  <div className="flex-shrink-0 mt-1">
                    {getActivityIcon(activity.activity_type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-sm font-medium text-white capitalize">
                        {activity.activity_type.replace('_', ' ')}
                      </p>
                      <p className="text-xs text-slate-400">
                        {new Date(activity.created_at).toLocaleDateString("es-ES", {
                          month: "short",
                          day: "numeric",
                          hour: "2-digit",
                          minute: "2-digit"
                        })}
                      </p>
                    </div>
                    <p className="text-sm text-slate-300 mb-2">{activity.description}</p>
                    {activity.performed_by && (
                      <p className="text-xs text-slate-500">Por: {activity.performed_by}</p>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-slate-400">
              <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No hay actividades registradas para este contacto.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
