// Script para probar el formulario de contacto
const testData = {
  name: "<PERSON>",
  email: "<EMAIL>", // Email especial de Resend para testing
  phone: "+1234567890",
  company: "Empresa Test",
  service: "Asistencia Informática",
  message: "Este es un mensaje de prueba para verificar que el sistema de emails funciona correctamente. Necesitamos soporte técnico para nuestros servidores.",
  budget: "5000-10000",
  timeline: "1-mes"
};

// Crear FormData
const formData = new FormData();
Object.keys(testData).forEach(key => {
  formData.append(key, testData[key]);
});

// Enviar la consulta
fetch('http://localhost:3000/api/contact', {
  method: 'POST',
  body: formData
})
.then(response => response.json())
.then(data => {
  console.log('Respuesta del servidor:', JSON.stringify(data, null, 2));
})
.catch(error => {
  console.error('Error:', error);
});
