import { NextRequest, NextResponse } from "next/server"
// import { stackServerApp } from "@/stack"

export async function middleware(request: NextRequest) {
  // Temporalmente deshabilitado para desarrollo
  // Solo aplicar middleware a rutas /admin
  /*
  if (request.nextUrl.pathname.startsWith('/admin')) {
    try {
      const user = await stackServerApp.getUser()
      
      if (!user) {
        // Redireccionar a la página de login
        return NextResponse.redirect(new URL('/admin-login', request.url))
      }
      
      // Usuario autenticado, continuar
      return NextResponse.next()
    } catch (error) {
      console.error('Error en middleware de autenticación:', error)
      return NextResponse.redirect(new URL('/admin-login', request.url))
    }
  }
  */
  
  return NextResponse.next()
}

export const config = {
  matcher: [
    '/admin/:path*'
  ]
} 