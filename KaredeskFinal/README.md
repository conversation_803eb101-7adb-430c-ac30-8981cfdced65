# Karedesk Website - Configuración de Emails

## 🚀 Configuración de Resend

### 1. Crear cuenta en Resend
1. Ve a [resend.com](https://resend.com)
2. Crea una cuenta gratuita
3. Verifica tu email

### 2. Obtener API Key
1. Ve al dashboard de Resend
2. Navega a "API Keys"
3. Crea una nueva API key
4. Copia la key (empieza con `re_`)

### 3. Configurar dominio (Opcional pero recomendado)
1. En Resend dashboard, ve a "Domains"
2. Agrega tu dominio (ej: karedesk.com)
3. Configura los registros DNS según las instrucciones
4. Verifica el dominio

### 4. Variables de entorno
Crea un archivo `.env.local` en la raíz del proyecto:

\`\`\`bash
# Resend Configuration
RESEND_API_KEY=re_tu_api_key_aqui

# Email addresses
CONTACT_EMAIL=<EMAIL>
SALES_EMAIL=<EMAIL>
SYSTEM_EMAIL=<EMAIL>
\`\`\`

### 5. Configuración de emails
Los emails se envían desde:
- **Cliente**: `<EMAIL>`
- **Sistema**: `<EMAIL>`

Los emails se reciben en:
- `<EMAIL>`
- `<EMAIL>`

## 📧 Características del sistema de emails

### Templates personalizados
- ✅ Email de confirmación al cliente
- ✅ Email de notificación al equipo
- ✅ Diseño responsive
- ✅ Colores por servicio
- ✅ Información estructurada

### Funcionalidades
- ✅ Validación de datos con Zod
- ✅ Manejo de errores robusto
- ✅ Templates HTML profesionales
- ✅ Información del cliente organizada
- ✅ Acciones rápidas para el equipo

### Servicios soportados
- 🔧 Asistencia Informática
- 🛡️ Análisis de Vulnerabilidades  
- 🧠 Consultoría IA
- 💻 Creación de Páginas Web

## 🔧 Desarrollo

### Instalar dependencias
\`\`\`bash
npm install
\`\`\`

### Ejecutar en desarrollo
\`\`\`bash
npm run dev
\`\`\`

### Testing de emails
Para probar el envío de emails en desarrollo, asegúrate de:
1. Tener configurada la `RESEND_API_KEY`
2. Usar emails reales para las pruebas
3. Verificar los logs en la consola

## 📊 Monitoreo

### Logs de Resend
- Ve al dashboard de Resend
- Sección "Logs" para ver emails enviados
- Métricas de entrega y apertura

### Debugging
Los logs incluyen:
- IDs de emails enviados
- Errores detallados
- Información de validación

## 🚀 Producción

### Antes del deploy
1. ✅ Configurar dominio verificado en Resend
2. ✅ Actualizar emails en variables de entorno
3. ✅ Probar envío de emails
4. ✅ Configurar monitoreo

### Variables de producción
\`\`\`bash
RESEND_API_KEY=re_production_key
CONTACT_EMAIL=<EMAIL>
SALES_EMAIL=<EMAIL>
SYSTEM_EMAIL=<EMAIL>
