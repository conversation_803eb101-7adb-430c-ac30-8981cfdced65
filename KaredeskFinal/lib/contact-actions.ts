"use server"

import { z } from "zod"
import { sendContactEmails } from "./email-service"
import { saveContact, findDuplicateContacts, addContactActivity } from "./database"

// Schema de validación para el formulario de contacto
const contactFormSchema = z.object({
  name: z.string().min(2, "El nombre debe tener al menos 2 caracteres"),
  email: z.string().email("Email inválido"),
  phone: z
    .string()
    .optional()
    .refine(
      (phone) => {
        if (!phone || phone.trim() === "") return true; // Opcional
        // Validación para números españoles
        const spanishPhoneRegex = /^(\+34|0034|34)?[6-9][0-9]{8}$/;
        const cleanPhone = phone.replace(/\s+/g, ""); // Remover espacios
        return spanishPhoneRegex.test(cleanPhone);
      },
      {
        message: "Formato de teléfono español inválido. Ej: +34 *********** o ***********",
      }
    ),
  company: z.string().optional(),
  service: z.string().min(1, "Servicio requerido"),
  message: z.string().min(10, "El mensaje debe tener al menos 10 caracteres"),
  budget: z.string().optional(),
  timeline: z.string().optional(),
})

export type ContactFormData = z.infer<typeof contactFormSchema>

export interface ContactFormState {
  success?: boolean
  message?: string
  errors?: Record<string, string[]>
  emailSent?: boolean
  contactId?: number
}

export async function submitContactForm(prevState: ContactFormState, formData: FormData): Promise<ContactFormState> {
  try {
    // Extraer datos del formulario
    const data = {
      name: formData.get("name") as string,
      email: formData.get("email") as string,
      phone: formData.get("phone") as string,
      company: formData.get("company") as string,
      service: formData.get("service") as string,
      message: formData.get("message") as string,
      budget: formData.get("budget") as string,
      timeline: formData.get("timeline") as string,
    }

    // Validar datos
    const validatedData = contactFormSchema.parse(data)

    // Verificar si ya existe una consulta reciente del mismo email
    const duplicates = await findDuplicateContacts(validatedData.email)
    const recentDuplicate = duplicates.find(
      (contact) => new Date(contact.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000), // últimas 24 horas
    )

    if (recentDuplicate) {
      // Agregar actividad al contacto existente
      await addContactActivity(
        recentDuplicate.id,
        "duplicate_submission",
        `Nueva consulta duplicada recibida: ${validatedData.message}`,
        "<EMAIL>",
      )

      return {
        success: true,
        message: `Hola ${validatedData.name}! 👋

Ya hemos recibido una consulta tuya recientemente. Nuestro equipo está revisando tu solicitud y te contactaremos pronto.

Si necesitas agregar información adicional o es urgente, puedes llamarnos directamente al +1 (555) 123-4567.

¡Gracias por tu paciencia!`,
        contactId: recentDuplicate.id,
      }
    }

    // Enviar emails primero
    const emailResult = await sendContactEmails(validatedData)

    if (!emailResult.success) {
      throw new Error("Error al enviar emails de confirmación")
    }

    // Guardar en base de datos
    const savedContact = await saveContact({
      ...validatedData,
      client_email_id: emailResult.clientEmailId,
      internal_email_id: emailResult.internalEmailId,
    })

    // Log para debugging
    console.log("Consulta guardada exitosamente:", {
      id: savedContact.id,
      email: savedContact.email,
      service: savedContact.service,
    })

    return {
      success: true,
      emailSent: true,
      contactId: savedContact.id,
      message: `¡Perfecto, ${validatedData.name}! 🎉 

Tu consulta sobre ${validatedData.service} ha sido registrada exitosamente.

📧 Te hemos enviado un email de confirmación a ${validatedData.email}
⏰ Nuestro equipo te contactará en las próximas 24 horas
🚀 Mientras tanto, puedes llamarnos al +1 (555) 123-4567

📋 Número de consulta: #${savedContact.id}

¡Gracias por confiar en Karedesk!`,
    }
  } catch (error) {
    console.error("Error en submitContactForm:", error)

    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Por favor corrige los errores en el formulario",
        errors: error.flatten().fieldErrors,
      }
    }

    // Error específico de email
    if (error instanceof Error && error.message.includes("email")) {
      return {
        success: false,
        message:
          "Tu consulta fue recibida pero hubo un problema enviando el email de confirmación. Te contactaremos pronto.",
      }
    }

    // Error de base de datos
    if (error instanceof Error && error.message.includes("base de datos")) {
      return {
        success: false,
        message:
          "Hubo un problema técnico procesando tu consulta. Por favor intenta nuevamente o contáctanos directamente al +1 (555) 123-4567.",
      }
    }

    return {
      success: false,
      message:
        "Hubo un error procesando tu consulta. Por favor intenta nuevamente o contáctanos directamente al +1 (555) 123-4567.",
    }
  }
}

// Función para obtener estadísticas de contactos (opcional)
export async function getContactStats() {
  // Aquí podrías implementar estadísticas desde tu base de datos
  return {
    totalContacts: 0,
    contactsThisMonth: 0,
    responseRate: 0,
  }
}
