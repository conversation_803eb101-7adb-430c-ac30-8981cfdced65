# Panel de Administración Karedesk

## 🚀 Descripción General

El Panel de Administración de Karedesk es una aplicación completa para gestionar consultas de clientes, construida con Next.js 15, TypeScript, Tailwind CSS y una base de datos serverless Neon PostgreSQL.

## ✨ Características Principales

### 📊 Dashboard Principal
- **Estadísticas en tiempo real**: Total de consultas, nuevas, tasa de respuesta, tiempo promedio
- **Filtros avanzados**: Por estado, servicio, prioridad y búsqueda de texto
- **Tabla interactiva**: Lista completa de consultas con acciones rápidas
- **Actualización automática**: Datos siempre actualizados

### 👤 Gestión de Contactos
- **Vista detallada**: Información completa de cada contacto
- **Historial de actividades**: Seguimiento completo de todas las interacciones
- **Actualización de estado**: Cambio de estado con notas y asignación
- **Acciones rápidas**: Email y llamadas directas desde la interfaz

### 📈 Analytics Avanzados
- **Métricas de rendimiento**: KPIs clave del negocio
- **Distribución por servicios**: Análisis de demanda por tipo de servicio
- **Estados de consultas**: Seguimiento del pipeline de ventas
- **Tendencias temporales**: Consultas por mes y semana

### ⚙️ Configuración del Sistema
- **Configuración de emails**: Gestión de direcciones de correo
- **Notificaciones**: Control de alertas y reportes
- **Respuesta automática**: Mensajes automáticos personalizables
- **Información empresarial**: Datos de contacto de la empresa

## 🛠️ Tecnologías Utilizadas

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Framer Motion
- **UI Components**: Radix UI, Lucide React
- **Base de Datos**: Neon PostgreSQL (Serverless)
- **ORM**: Neon Serverless Driver
- **Validación**: Zod
- **Email**: Resend

## 📁 Estructura del Proyecto

```
KaredeskFinal/
├── app/
│   ├── admin/                    # Panel de administración
│   │   ├── page.tsx             # Dashboard principal
│   │   ├── contacts/[id]/       # Vista detallada de contactos
│   │   ├── analytics/           # Dashboard de analytics
│   │   └── settings/            # Configuración del sistema
│   ├── api/
│   │   └── admin/               # APIs del panel de admin
│   │       ├── contacts/        # CRUD de contactos
│   │       ├── stats/           # Estadísticas
│   │       └── bulk/            # Operaciones masivas
│   └── ...
├── lib/
│   ├── database.ts              # Funciones de base de datos
│   ├── email-service.ts         # Servicio de email
│   └── utils.ts                 # Utilidades
├── components/
│   └── ui/                      # Componentes de UI
└── ...
```

## 🗄️ Esquema de Base de Datos

### Tabla `contacts`
```sql
CREATE TABLE contacts (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50),
  company VARCHAR(255),
  service VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  budget VARCHAR(100),
  timeline VARCHAR(100),
  status VARCHAR(50) DEFAULT 'new',
  priority VARCHAR(20) DEFAULT 'medium',
  assigned_to VARCHAR(255),
  notes TEXT,
  client_email_id VARCHAR(255),
  internal_email_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  responded_at TIMESTAMP,
  closed_at TIMESTAMP
);
```

### Tabla `contact_activities`
```sql
CREATE TABLE contact_activities (
  id SERIAL PRIMARY KEY,
  contact_id INTEGER REFERENCES contacts(id) ON DELETE CASCADE,
  activity_type VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  performed_by VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🚀 Instalación y Configuración

### 1. Clonar el repositorio
```bash
git clone <repository-url>
cd KaredeskFinal
```

### 2. Instalar dependencias
```bash
npm install
```

### 3. Configurar variables de entorno
Crear archivo `.env.local`:
```env
# Database Configuration (Neon/PostgreSQL)
DATABASE_URL=postgresql://username:<EMAIL>/neondb?sslmode=require

# Resend Configuration
RESEND_API_KEY=re_your_api_key_here

# Email addresses
CONTACT_EMAIL=<EMAIL>
SALES_EMAIL=<EMAIL>
SYSTEM_EMAIL=<EMAIL>

# Next.js Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 4. Ejecutar migraciones de base de datos
Las tablas se crean automáticamente al ejecutar la aplicación por primera vez.

### 5. Iniciar el servidor de desarrollo
```bash
npm run dev
```

## 📱 Uso del Panel de Administración

### Acceso
- URL: `http://localhost:3000/admin`
- No requiere autenticación (en desarrollo)

### Funcionalidades Principales

#### Dashboard Principal
1. **Estadísticas**: Vista general de métricas clave
2. **Filtros**: Buscar y filtrar consultas
3. **Tabla de consultas**: Lista completa con acciones

#### Gestión de Contactos
1. **Ver detalles**: Click en el ícono de ojo
2. **Actualizar estado**: Cambiar estado y agregar notas
3. **Asignar responsable**: Asignar consultas a miembros del equipo
4. **Historial**: Ver todas las actividades del contacto

#### Analytics
1. **KPIs**: Métricas de rendimiento
2. **Distribuciones**: Gráficos por servicio y estado
3. **Tendencias**: Análisis temporal

#### Configuración
1. **Emails**: Configurar direcciones de correo
2. **Notificaciones**: Activar/desactivar alertas
3. **Respuesta automática**: Personalizar mensajes
4. **Sistema**: Parámetros generales

## 🔧 API Endpoints

### Contactos
- `GET /api/admin/contacts` - Listar contactos
- `GET /api/admin/contacts/[id]` - Obtener contacto específico
- `PATCH /api/admin/contacts/[id]` - Actualizar contacto
- `POST /api/admin/contacts/[id]` - Agregar actividad

### Estadísticas
- `GET /api/admin/stats` - Obtener estadísticas

### Operaciones Masivas
- `POST /api/admin/bulk` - Operaciones en lote
- `GET /api/admin/bulk?format=csv` - Exportar datos

## 🎨 Personalización

### Colores y Tema
Los colores se pueden personalizar en `tailwind.config.ts`:
```typescript
colors: {
  teal: { /* colores personalizados */ },
  slate: { /* colores personalizados */ }
}
```

### Componentes UI
Los componentes están en `components/ui/` y pueden ser personalizados según las necesidades.

## 📊 Métricas y Analytics

El sistema incluye las siguientes métricas:
- **Total de consultas**
- **Consultas nuevas**
- **Tasa de respuesta**
- **Tiempo promedio de respuesta**
- **Distribución por servicios**
- **Estados de consultas**
- **Tendencias temporales**

## 🔒 Seguridad

- Validación de datos con Zod
- Sanitización de consultas SQL
- Variables de entorno para configuración sensible
- Conexión segura a base de datos (SSL)

## 🚀 Despliegue

### Vercel (Recomendado)
1. Conectar repositorio a Vercel
2. Configurar variables de entorno
3. Desplegar automáticamente

### Netlify
1. Conectar repositorio a Netlify
2. Configurar build settings
3. Agregar variables de entorno

## 📝 Próximas Funcionalidades

- [ ] Autenticación y autorización
- [ ] Roles y permisos
- [ ] Exportación avanzada (PDF, Excel)
- [ ] Integración con CRM
- [ ] Notificaciones push
- [ ] Dashboard móvil
- [ ] Automatizaciones avanzadas

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama de feature (`git checkout -b feature/AmazingFeature`)
3. Commit cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abrir Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE.md](LICENSE.md) para detalles.

## 📞 Soporte

Para soporte técnico, contactar:
- Email: <EMAIL>
- Documentación: [docs.karedesk.com](https://docs.karedesk.com)

---

**Desarrollado con ❤️ por el equipo de Karedesk**
