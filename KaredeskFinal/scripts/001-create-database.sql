-- Crear tabla de consultas
CREATE TABLE IF NOT EXISTS contacts (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    company VARCHAR(255),
    service VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    budget VARCHAR(50),
    timeline VARCHAR(50),
    status VARCHAR(50) DEFAULT 'new',
    priority VARCHAR(20) DEFAULT 'medium',
    assigned_to VA<PERSON><PERSON>R(255),
    notes TEXT,
    client_email_id VARCHAR(255),
    internal_email_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP WITH TIME ZONE,
    closed_at TIMESTAMP WITH TIME ZONE
);

-- Crear índices para optimizar consultas
CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email);
CREATE INDEX IF NOT EXISTS idx_contacts_service ON contacts(service);
CREATE INDEX IF NOT EXISTS idx_contacts_status ON contacts(status);
CREATE INDEX IF NOT EXISTS idx_contacts_created_at ON contacts(created_at);
CREATE INDEX IF NOT EXISTS idx_contacts_priority ON contacts(priority);

-- Crear tabla de actividades/seguimiento
CREATE TABLE IF NOT EXISTS contact_activities (
    id SERIAL PRIMARY KEY,
    contact_id INTEGER REFERENCES contacts(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL, -- 'email_sent', 'call_made', 'meeting_scheduled', 'note_added', etc.
    description TEXT NOT NULL,
    performed_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Crear índice para actividades
CREATE INDEX IF NOT EXISTS idx_activities_contact_id ON contact_activities(contact_id);
CREATE INDEX IF NOT EXISTS idx_activities_created_at ON contact_activities(created_at);

-- Crear tabla de configuración del sistema
CREATE TABLE IF NOT EXISTS system_config (
    key VARCHAR(100) PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insertar configuración inicial
INSERT INTO system_config (key, value, description) VALUES 
    ('auto_assign_enabled', 'true', 'Asignación automática de consultas'),
    ('default_assignee', '<EMAIL>', 'Asignado por defecto'),
    ('response_time_target', '24', 'Tiempo objetivo de respuesta en horas'),
    ('high_priority_budget', '10000', 'Presupuesto mínimo para alta prioridad')
ON CONFLICT (key) DO NOTHING;

-- Función para actualizar timestamp de updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para actualizar updated_at automáticamente
CREATE TRIGGER update_contacts_updated_at 
    BEFORE UPDATE ON contacts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
