# 🔐 Configuración de Usuarios Administradores - Stack Auth

## 📋 Guía Completa para Configurar Administradores

### 🎯 **Paso 1: Acceder al Dashboard de Stack Auth**

1. **Ir al Dashboard**: Visita [https://app.stack-auth.com](https://app.stack-auth.com)
2. **Iniciar <PERSON>**: Usa tu cuenta de Stack Auth
3. **Seleccionar Proyecto**: Elige tu proyecto con ID: `53077234-96e2-4d46-8477-2437d8676d99`

### 🔧 **Paso 2: Configurar el Primer Usuario Administrador**

#### **Opción A: Crear Admin desde el Dashboard**
1. Ve a **"Users"** en el sidebar
2. Click en **"Create User"** 
3. Completa los datos:
   - **Email**: `<EMAIL>` (o tu email preferido)
   - **Password**: Crea una contraseña segura
   - **Display Name**: `Administrador <PERSON>`
4. **Guardar** el usuario

#### **Opción B: Registrarse desde la App y Promover**
1. Ve a tu app: `http://localhost:3000/admin-login`
2. **Sign Up** con tu email de administrador
3. Vuelve al Dashboard de Stack Auth
4. Encuentra tu usuario en la lista
5. **Edita** el usuario y márcalo como administrador

### ⚙️ **Paso 3: Configurar Roles y Permisos (Opcional)**

#### **Crear Rol de Administrador:**
1. En el Dashboard, ve a **"Teams & Permissions"**
2. Click en **"Create Permission"**
3. Crea permisos como:
   - `admin:read` - Ver panel admin
   - `admin:write` - Editar configuraciones
   - `admin:delete` - Eliminar datos
   - `contacts:manage` - Gestionar contactos

#### **Asignar Permisos:**
1. Ve a **"Users"**
2. Selecciona tu usuario administrador
3. En **"Permissions"**, asigna los permisos creados

### 🛡️ **Paso 4: Mejorar la Seguridad del Admin**

#### **Habilitar 2FA (Recomendado):**
1. En tu perfil de usuario
2. Ve a **"Security"**
3. **Enable Two-Factor Authentication**
4. Escanea el código QR con tu app de autenticación

#### **Configurar Email de Recuperación:**
1. Verifica que el email esté confirmado
2. Configura preguntas de seguridad si están disponibles

### 📧 **Paso 5: Configurar Notificaciones de Admin**

1. En **"Settings"** → **"Notifications"**
2. Habilita notificaciones para:
   - Nuevos registros de usuarios
   - Intentos de login fallidos
   - Cambios de configuración

### 🔄 **Paso 6: Probar el Acceso Admin**

1. **Cerrar sesión** de tu app actual
2. Ve a: `http://localhost:3000/admin-login`
3. **Inicia sesión** con las credenciales de admin
4. Verifica que puedas acceder a: `http://localhost:3000/admin`
5. **Confirma** que todas las secciones funcionan:
   - Dashboard principal
   - Lista de contactos
   - Analytics
   - Configuración

### 👥 **Paso 7: Agregar Más Administradores**

#### **Para cada nuevo admin:**
1. **Método 1 - Invitación por Email:**
   - En el Dashboard: **"Users"** → **"Invite User"**
   - Envía invitación por email
   - El usuario se registra usando el link

2. **Método 2 - Crear Directamente:**
   - **"Users"** → **"Create User"**
   - Proporciona credenciales temporales
   - El usuario cambia la contraseña en el primer login

### 🚀 **Configuración de Producción**

#### **Variables de Entorno para Producción:**
```env
# Stack Auth Production
NEXT_PUBLIC_STACK_PROJECT_ID=53077234-96e2-4d46-8477-2437d8676d99
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=pck_6bzwdwwqbe456txn4aahsnsk2rm9actqmq79vam99r118
STACK_SECRET_SERVER_KEY=ssk_sgqrfq5dq6e59cgkmbrmanqz2xcm8m16y8x1cv5nqbkpg

# URLs de Producción
NEXT_PUBLIC_APP_URL=https://tu-dominio.vercel.app
```

#### **Configurar en Vercel:**
1. Ve a tu proyecto en Vercel
2. **Settings** → **Environment Variables**
3. Agrega todas las variables de Stack Auth
4. **Redeploy** el proyecto

### 🔒 **Mejores Prácticas de Seguridad**

#### **Para Administradores:**
- ✅ Usar contraseñas únicas y seguras
- ✅ Habilitar 2FA siempre
- ✅ No compartir credenciales
- ✅ Revisar logs de acceso regularmente
- ✅ Cerrar sesión en dispositivos compartidos

#### **Para el Sistema:**
- ✅ Limitar número de intentos de login
- ✅ Configurar alertas de seguridad
- ✅ Revisar permisos periódicamente
- ✅ Mantener Stack Auth actualizado
- ✅ Backup regular de configuraciones

### 📱 **URLs Importantes**

- **Dashboard Stack Auth**: https://app.stack-auth.com
- **Documentación**: https://docs.stack-auth.com
- **Login Admin Local**: http://localhost:3000/admin-login
- **Panel Admin Local**: http://localhost:3000/admin
- **Login Admin Producción**: https://tu-dominio.vercel.app/admin-login

### 🆘 **Solución de Problemas**

#### **No puedo acceder al admin:**
1. Verifica que el usuario esté creado en Stack Auth
2. Confirma que las variables de entorno son correctas
3. Revisa que el middleware esté funcionando
4. Chequea los logs del servidor

#### **Error de autenticación:**
1. Regenera las API keys en Stack Auth
2. Actualiza las variables de entorno
3. Reinicia el servidor de desarrollo
4. Limpia caché del navegador

#### **No aparece el panel admin:**
1. Verifica que el layout de admin esté protegido
2. Confirma que el middleware redirija correctamente
3. Revisa que Stack Auth esté configurado en el layout principal

### 📞 **Soporte**

- **Stack Auth Discord**: https://discord.gg/stack-auth
- **GitHub Issues**: https://github.com/stack-auth/stack
- **Documentación**: https://docs.stack-auth.com

---

**¡Configuración completada!** 🎉

Tu panel de administración está ahora protegido con Stack Auth y listo para usar en producción. 